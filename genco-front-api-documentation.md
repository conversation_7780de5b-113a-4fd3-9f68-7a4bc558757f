# GENCO前台用户系统API接口文档

## 项目概述

GENCO是一个基于Spring Boot的电商系统项目，采用多模块架构。本文档详细介绍genco-front前台用户系统的所有API接口。

### 项目信息
- **项目名称**: GENCO电商系统前台
- **端口**: 8081
- **技术栈**: Spring Boot 2.2.6, MyBatis Plus, Redis, JWT
- **API文档**: Swagger2
- **基础路径**: `/api/front`

## 接口分类

### 1. 用户认证与登录

**Controller**: `LoginController`  
**路径前缀**: `/api/front`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| POST | `/login/mobile` | 手机号登录 | LoginMobileRequest |
| POST | `/login` | 账号密码登录 | LoginRequest |
| GET | `/logout` | 退出登录 | - |
| POST | `/sendCode` | 发送短信登录验证码 | phone |
| POST | `/authorize/login` | TikTok授权登录 | code, code_verifier, spread_spid |

### 2. 用户中心管理

**Controller**: `UserController`  
**路径前缀**: `/api/front`

#### 基础用户信息
| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| POST | `/register/reset` | 手机号修改密码 | PasswordRequest |
| POST | `/user/edit` | 修改个人资料 | UserEditRequest |
| GET | `/user` | 个人中心用户信息 | - |
| POST | `/update/binding/verify` | 换绑手机号校验 | UserBindingPhoneUpdateRequest |
| POST | `/update/binding` | 换绑手机号 | UserBindingPhoneUpdateRequest |
| GET | `/menu/user` | 获取个人中心菜单 | - |

#### 推广分销功能
| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/user/revenue` | 推广数据接口(佣金统计) | - |
| GET | `/spread/commission/detail` | 推广佣金明细 | PageParamRequest |
| GET | `/spread/count/{type}` | 推广佣金/提现总和 | type(3=佣金,4=提现) |
| GET | `/spread/people/count` | 推广人统计 | - |
| GET | `/spread/people` | 推广人列表 | UserSpreadPeopleRequest, PageParamRequest |
| GET | `/spread/team` | 团队推广情况 | UserSpreadPeopleRequest |
| GET | `/spread/order` | 推广订单 | PageParamRequest |
| GET | `/rank` | 推广人排行 | type, PageParamRequest |
| GET | `/brokerage_rank` | 佣金排行 | type, PageParamRequest |
| GET | `/user/brokerageRankNumber` | 当前用户佣金排名 | type |
| GET | `/user/spread/banner` | 推广海报图 | - |
| GET | `/user/bindSpread` | 绑定推广关系 | spreadPid |
| POST | `/user/bindInviteCode` | 绑定邀请码 | inviteCode |

#### 提现功能
| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| POST | `/extract/cash` | 提现申请 | UserExtractRequest |
| GET | `/extract/record` | 提现记录 | PageParamRequest |
| GET | `/extract/user` | 提现用户信息 | - |
| GET | `/extract/bank` | 提现银行列表 | - |

#### 积分与等级
| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/integral/user` | 用户积分信息 | - |
| GET | `/integral/list` | 积分记录 | PageParamRequest |
| GET | `/user/expList` | 经验记录 | PageParamRequest |
| GET | `/user/level/grade` | 会员等级列表 | - |
| GET | `/user/balance` | 用户资金统计 | - |

### 3. 用户签到

**Controller**: `UserSignController`  
**路径前缀**: `/api/front/user/sign`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/list` | 签到列表 | PageParamRequest |
| GET | `/month` | 签到列表(年月维度) | PageParamRequest |
| GET | `/config` | 签到配置 | - |
| GET | `/integral` | 执行签到 | - |
| GET | `/get` | 今日签到记录详情 | - |
| POST | `/user` | 签到用户信息 | - |

### 4. 用户地址管理

**Controller**: `UserAddressController`  
**路径前缀**: `/api/front/address`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/list` | 地址列表 | PageParamRequest |
| POST | `/edit` | 保存/编辑地址 | UserAddressRequest |
| POST | `/del` | 删除地址 | UserAddressDelRequest |
| GET | `/detail/{id}` | 地址详情 | id |
| POST | `/default/{id}` | 设置默认地址 | id |

### 5. 商品相关

**Controller**: `ProductController`  
**路径前缀**: `/api/front`

#### 商品浏览
| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/product/hot` | 热门商品推荐 | PageParamRequest |
| GET | `/products` | 商品列表 | ProductRequest, PageParamRequest |
| GET | `/product/list` | 商品列表(分类模型) | ProductListRequest, PageParamRequest |
| GET | `/product/detail/{id}` | 商品详情 | id |
| GET | `/product/sku/detail/{id}` | 商品规格详情 | id |
| GET | `/category` | 获取分类 | - |
| GET | `/product/leaderboard` | 商品排行榜 | - |

#### 商品评价
| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/reply/list/{id}` | 商品评论列表 | id, type(0=全部,1=好评,2=中评,3=差评), PageParamRequest |
| GET | `/reply/config/{id}` | 商品评论数量 | id |
| GET | `/reply/product/{id}` | 商品详情评论 | id |

#### 商品同步(TikTok)
| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/product/async/{productId}` | 商品同步 | productId |
| GET | `/token/refresh` | 刷新TikTok访问令牌 | - |

### 6. 购物车管理

**Controller**: `CartController`  
**路径前缀**: `/api/front/cart`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/list` | 购物车列表 | isValid(true=有效,false=无效), PageParamRequest |
| POST | `/save` | 添加到购物车 | CartRequest |
| POST | `/delete` | 删除购物车商品 | ids |
| POST | `/num` | 修改商品数量 | id, number |
| GET | `/count` | 获取购物车数量 | CartNumRequest |
| POST | `/resetcart` | 购物车重选提交 | CartResetRequest |

### 7. 订单管理

**Controller**: `StoreOrderController`  
**路径前缀**: `/api/front/order`

#### 订单创建与查询
| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| POST | `/pre/order` | 预下单 | PreOrderRequest |
| GET | `/load/pre/{preOrderNo}` | 加载预下单 | preOrderNo |
| POST | `/computed/price` | 计算订单价格 | OrderComputedPriceRequest |
| POST | `/create` | 创建订单 | CreateOrderRequest |
| GET | `/list` | 订单列表 | type(0=未支付,1=待发货,2=待收货,3=待评价,4=已完成,-3=售后), PageParamRequest |
| GET | `/detail/{orderId}` | 订单详情 | orderId |
| GET | `/data` | 订单头部数量统计 | - |

#### 订单操作
| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| POST | `/del` | 删除订单 | id |
| POST | `/comment` | 评价订单 | StoreProductReplyAddRequest |
| POST | `/take` | 订单收货 | id |
| POST | `/cancel` | 订单取消 | id |
| POST | `/async` | 订单同步 | - |

#### 退款相关
| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/apply/refund/{orderId}` | 获取申请退款信息 | orderId |
| POST | `/refund` | 订单退款申请 | OrderRefundApplyRequest |
| GET | `/refund/reason` | 订单退款理由 | - |

#### 其他功能
| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/express/{orderId}` | 物流信息查询 | orderId |
| POST | `/product` | 待评价商品信息查询 | GetProductReply |
| GET | `/get/pay/config` | 获取支付配置 | - |

### 8. 支付管理

**Controller**: `PayController`  
**路径前缀**: `/api/front/pay`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| POST | `/payment` | 订单支付 | OrderPayRequest |
| GET | `/queryPayResult` | 查询支付结果(兼容旧版本) | orderNo |
| GET | `/query` | 查询支付结果(新版本) | orderNo |

### 9. 用户充值

**Controller**: `UserRechargeController`  
**路径前缀**: `/api/front/recharge`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/index` | 充值额度选择 | - |
| POST | `/routine` | 小程序充值 | UserRechargeRequest |
| POST | `/create` | 充值下单 | UserRechargeRequest |

### 10. 收藏管理

**Controller**: `UserCollectController`  
**路径前缀**: `/api/front/collect`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/user` | 我的收藏列表 | PageParamRequest |
| POST | `/add` | 添加收藏产品 | UserCollectRequest |
| POST | `/all` | 批量收藏 | UserCollectAllRequest |
| POST | `/delete` | 取消收藏产品 | requestJson |
| POST | `/cancel/{proId}` | 取消收藏产品(通过商品ID) | proId |

### 11. 优惠券管理

**Controller**: `CouponController` & `UserCouponController`
**路径前缀**: `/api/front`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/coupons/list` | 优惠券列表 | PageParamRequest |
| GET | `/coupons/order/{preOrderNo}` | 当前订单可用优惠券 | preOrderNo |
| GET | `/user/coupons` | 我的优惠券列表 | status(0=未使用,1=已使用,2=已过期), PageParamRequest |
| POST | `/user/coupon/receive` | 领券 | couponId |

### 12. 营销活动

#### 秒杀活动
**Controller**: `SecKillController`
**路径前缀**: `/api/front/seckill`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/index` | 秒杀首页数据 | - |
| GET | `/header` | 秒杀Header | - |
| GET | `/list/{timeId}` | 秒杀列表 | timeId, PageParamRequest |
| GET | `/detail/{id}` | 秒杀详情 | id |

#### 砍价活动
**Controller**: `BargainController`
**路径前缀**: `/api/front/bargain`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/index` | 砍价首页信息 | - |
| GET | `/header` | 砍价商品列表header | - |
| GET | `/list` | 砍价商品列表 | PageParamRequest |
| GET | `/user` | 获取用户砍价信息 | BargainFrontRequest |
| GET | `/detail/{id}` | 砍价商品详情 | id |
| POST | `/start` | 创建砍价活动 | BargainFrontRequest |
| POST | `/help` | 砍价帮助 | BargainFrontRequest |
| GET | `/record` | 砍价记录 | PageParamRequest |

#### 拼团活动
**Controller**: `CombinationController`
**路径前缀**: `/api/front/combination`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/index` | 拼团首页数据 | - |
| GET | `/header` | 拼团商品列表header | - |
| GET | `/list` | 拼团商品列表 | PageParamRequest |
| GET | `/detail/{id}` | 拼团商品详情 | id |
| GET | `/pink/{pinkId}` | 去拼团 | pinkId |
| GET | `/more` | 更多拼团 | comId, PageParamRequest |
| POST | `/remove` | 取消拼团 | StorePinkRequest |

### 13. 微信功能

**Controller**: `WeChatController`
**路径前缀**: `/api/front/wechat`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/authorize/login` | 微信公众号授权登录 | code, spread_spid |
| POST | `/authorize/program/login` | 微信小程序授权登录 | code, RegisterThirdUserRequest |
| POST | `/register/binding/phone` | 微信注册绑定手机号 | WxBindingPhoneRequest |
| GET | `/config` | 获取微信公众号js配置 | url |
| GET | `/getLogo` | 小程序获取授权logo | - |
| GET | `/program/my/temp/list` | 订阅消息模板列表 | type |

### 14. 其他功能

#### 首页
**Controller**: `IndexController`
**路径前缀**: `/api/front`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/index` | 首页数据 | - |
| GET | `/index/product/{type}` | 首页商品列表 | type, PageParamRequest |
| GET | `/search` | 搜索 | keyword, PageParamRequest |
| GET | `/search/keyword` | 热门搜索关键字 | - |
| GET | `/share` | 获取分享配置 | - |

#### 品牌
**Controller**: `BrandController`
**路径前缀**: `/api/front`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/brand/list` | 品牌列表 | PageParamRequest |

#### 城市服务
**Controller**: `CityController`
**路径前缀**: `/api/front/city`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/list` | 城市服务树形结构数据 | - |

#### 提货点
**Controller**: `StoreController`
**路径前缀**: `/api/front/store`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| POST | `/list` | 附近的提货点 | latitude, longitude, PageParamRequest |

#### 文章
**Controller**: `ArticleController`
**路径前缀**: `/api/front/article`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/list/{cid}` | 文章分页列表 | cid, PageParamRequest |
| GET | `/details/{id}` | 文章详情 | id |
| GET | `/hot/list` | 热门文章 | - |

#### 二维码服务
**Controller**: `QrCodeController`
**路径前缀**: `/api/front/qrcode`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| POST | `/get` | 获取二维码 | url |
| POST | `/base64` | 远程图片转base64 | url |

#### 文件上传
**Controller**: `UploadFrontController`
**路径前缀**: `/api/front/user/upload`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| POST | `/image` | 图片上传 | multipart, model, pid |

## 技术特点

### 认证与安全
- **JWT Token认证**: 所有需要登录的接口都需要在Header中携带JWT Token
- **多端登录支持**: 支持手机号、账号密码、微信公众号、小程序等多种登录方式
- **TikTok集成**: 支持TikTok Shop授权登录和商品同步

### 数据处理
- **统一响应格式**: 所有接口返回统一的`CommonResult<T>`格式
- **分页支持**: 大部分列表接口支持`PageParamRequest`分页参数
- **参数验证**: 使用`@Validated`注解进行请求参数验证

### 业务功能
- **完整电商流程**: 涵盖商品浏览、购物车、下单、支付、物流等完整流程
- **营销活动**: 支持秒杀、砍价、拼团等多种营销活动
- **分销系统**: 完整的推广分销功能，支持多级分销
- **会员体系**: 积分、等级、签到等会员功能
- **多支付方式**: 支持微信支付等多种支付方式

### 第三方集成
- **微信生态**: 完整支持微信公众号、小程序
- **TikTok Shop**: 支持TikTok商品同步和订单管理
- **云存储**: 支持阿里云OSS、腾讯云COS、七牛云等多种云存储
- **物流查询**: 集成物流查询接口

## 接口调用说明

### 请求头设置
```
Content-Type: application/json
Authorization: Bearer {JWT_TOKEN}  // 需要登录的接口
```

### 响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据
  }
}
```

### 分页参数
```json
{
  "page": 1,
  "limit": 20
}
```

### 分页响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "list": [],
    "total": 100,
    "pageNum": 1,
    "pageSize": 20,
    "pages": 5
  }
}
```

---

**文档版本**: v1.0
**更新时间**: 2025-07-28
**维护人员**: 开发团队
