# 新邀请奖励规则实现方案

## 需求描述

将现有的"绑定即奖励"邀请机制改为"首单完成后奖励"机制：

**A拉新B → B填写邀请码完成邀请绑定 → B完成一笔TikTok关联订单且状态为已结算 → A获得固定5元奖励**

## 现状分析

### 当前邀请奖励机制
1. **触发时机**：用户绑定邀请码时立即触发
2. **奖励计算**：基于会员等级的复杂奖励矩阵
3. **处理流程**：`bindInviteCode` → 创建 `INVITE_REWARD` 任务 → `InviteRewardProcessor` 处理

### TikTok订单机制
1. **订单类型**：`StoreOrder.type = 2` 表示外部订单（TikTok订单）
2. **已结算状态**：`StoreOrder.status = 6` 表示 `SETTLED`（已结算）
3. **现有处理**：订单状态变为 `SETTLED` 时创建 `ORDER_REWARD` 任务

## 实现方案

### 方案一：修改现有邀请奖励机制（推荐）

#### 1. 新增配置常量
在 `Constants.java` 中添加：
```java
// 首单邀请奖励功能开关
public static final String FIRST_ORDER_INVITE_REWARD_ENABLE = "first_order_invite_reward_enable";

// 首单邀请奖励金额（固定5元）
public static final String FIRST_ORDER_INVITE_REWARD_AMOUNT = "first_order_invite_reward_amount";

// 首单邀请奖励模式开关（0-绑定即奖励，1-首单完成后奖励）
public static final String FIRST_ORDER_INVITE_REWARD_MODE = "first_order_invite_reward_mode";
```

#### 2. 修改邀请绑定逻辑
在 `UserServiceImpl.bindInviteCode()` 方法中：
```java
// 原有逻辑：立即创建邀请奖励任务
// userRewardTaskService.createInviteRewardTask(currentUser.getUid(), inviter.getUid(), null);

// 新逻辑：根据配置决定是否立即奖励
String rewardMode = systemConfigService.getValueByKey(Constants.FIRST_ORDER_INVITE_REWARD_MODE);
if (!"1".equals(rewardMode)) {
    // 传统模式：绑定即奖励
    userRewardTaskService.createInviteRewardTask(currentUser.getUid(), inviter.getUid(), null);
}
// 首单模式：不立即创建奖励任务，等待首单完成
```

#### 3. 新增首单邀请奖励任务类型
在 `UserRewardTask.java` 中添加：
```java
/**
 * 首单邀请奖励任务类型常量
 */
public static final String TASK_TYPE_FIRST_ORDER_INVITE_REWARD = "FIRST_ORDER_INVITE_REWARD";
```

#### 4. 修改TikTok订单结算逻辑
在 `TiktokOrderSyncServiceImpl.nextByOrderStatus()` 方法中：
```java
private void nextByOrderStatus(StoreOrder storeOrder, Integer nextStatus) {
    if (nextStatus == 6) { // SETTLED 已结算
        // 原有逻辑：创建订单返佣任务
        userRewardTaskService.createOrderRewardTask(storeOrder.getUid(), storeOrder.getOrderId(),
                null, JSONUtil.toJsonStr(storeOrder));
        
        // 新增逻辑：检查是否为用户首单，如果是则创建首单邀请奖励任务
        checkAndCreateFirstOrderInviteReward(storeOrder);
    }
}

private void checkAndCreateFirstOrderInviteReward(StoreOrder storeOrder) {
    // 1. 检查首单邀请奖励功能是否启用
    String enableConfig = systemConfigService.getValueByKey(Constants.FIRST_ORDER_INVITE_REWARD_ENABLE);
    if (!"1".equals(enableConfig)) return;
    
    // 2. 检查是否为首单邀请奖励模式
    String rewardMode = systemConfigService.getValueByKey(Constants.FIRST_ORDER_INVITE_REWARD_MODE);
    if (!"1".equals(rewardMode)) return;
    
    // 3. 检查是否为TikTok订单
    if (storeOrder.getType() == null || !storeOrder.getType().equals(2)) return;
    
    // 4. 获取用户信息和邀请人信息
    User user = userService.getInfoByUid(storeOrder.getUid());
    if (user == null || user.getSpreadUid() == null || user.getSpreadUid() <= 0) return;
    
    // 5. 检查是否为用户的首笔TikTok已结算订单
    if (!isUserFirstSettledTikTokOrder(storeOrder.getUid(), storeOrder.getId())) return;
    
    // 6. 创建首单邀请奖励任务
    userRewardTaskService.createFirstOrderInviteRewardTask(
        user.getUid(), 
        user.getSpreadUid(), 
        storeOrder.getOrderId(),
        JSONUtil.toJsonStr(storeOrder)
    );
}
```

#### 5. 新增首单邀请奖励处理器
创建 `FirstOrderInviteRewardProcessor.java`：
```java
@Component
public class FirstOrderInviteRewardProcessor implements UserRewardTaskProcessor {
    
    @Override
    public boolean supports(String taskType) {
        return UserRewardTask.TASK_TYPE_FIRST_ORDER_INVITE_REWARD.equals(taskType);
    }
    
    @Override
    public void process(UserRewardTask task) {
        // 1. 检查功能开关
        String enableConfig = systemConfigService.getValueByKey(Constants.FIRST_ORDER_INVITE_REWARD_ENABLE);
        if (!"1".equals(enableConfig)) return;
        
        // 2. 获取固定奖励金额（5元）
        String amountConfig = systemConfigService.getValueByKey(Constants.FIRST_ORDER_INVITE_REWARD_AMOUNT);
        BigDecimal rewardAmount = new BigDecimal(amountConfig);
        
        // 3. 获取邀请人信息
        User inviter = userService.getInfoByUid(task.getInviteUid());
        if (inviter == null) {
            throw new RuntimeException("邀请人不存在，uid=" + task.getInviteUid());
        }
        
        // 4. 创建奖励账单
        UserBill bill = new UserBill();
        bill.setUid(inviter.getUid());
        bill.setLinkId(task.getOrderId());
        bill.setCategory("invite_first_order");
        bill.setType("income");
        bill.setNumber(rewardAmount);
        bill.setBalance(inviter.getNowMoney().add(rewardAmount));
        bill.setMark("首单邀请奖励，被邀请人完成首笔TikTok订单");
        bill.setStatus(1);
        bill.setCreateTime(new Date());
        
        // 5. 保存账单并更新用户余额
        userBillService.save(bill);
        inviter.setNowMoney(inviter.getNowMoney().add(rewardAmount));
        userService.updateById(inviter);
    }
}
```

#### 6. 扩展UserRewardTaskService
在 `UserRewardTaskService.java` 中添加：
```java
/**
 * 创建首单邀请奖励任务
 */
void createFirstOrderInviteRewardTask(Integer userId, Integer inviteUid, String orderId, String contextJson);
```

在 `UserRewardTaskServiceImpl.java` 中实现：
```java
@Override
public void createFirstOrderInviteRewardTask(Integer userId, Integer inviteUid, String orderId, String contextJson) {
    UserRewardTask task = new UserRewardTask();
    task.setUserId(userId);
    task.setInviteUid(inviteUid);
    task.setOrderId(orderId);
    task.setTaskType(UserRewardTask.TASK_TYPE_FIRST_ORDER_INVITE_REWARD);
    task.setStatus("PENDING");
    task.setContext(contextJson);
    addTask(task);
}
```

#### 7. 新增辅助方法
在 `StoreOrderService.java` 中添加：
```java
/**
 * 检查是否为用户首笔已结算的TikTok订单
 */
boolean isUserFirstSettledTikTokOrder(Integer uid, Integer currentOrderId);
```

实现：
```java
@Override
public boolean isUserFirstSettledTikTokOrder(Integer uid, Integer currentOrderId) {
    LambdaQueryWrapper<StoreOrder> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(StoreOrder::getUid, uid)
                .eq(StoreOrder::getType, 2)  // TikTok订单
                .eq(StoreOrder::getStatus, 6) // 已结算
                .orderByAsc(StoreOrder::getId);
    
    List<StoreOrder> orders = list(queryWrapper);
    return orders.size() == 1 && orders.get(0).getId().equals(currentOrderId);
}
```

### 方案二：独立的首单奖励机制（备选）

如果不想影响现有邀请奖励机制，可以创建完全独立的首单奖励系统：

1. 新增 `eb_first_order_invite_record` 表记录首单邀请关系
2. 在订单结算时检查并处理首单奖励
3. 与现有邀请奖励机制并行运行

## 数据库配置

需要在 `eb_system_config` 表中添加以下配置：

```sql
-- 首单邀请奖励功能开关
INSERT INTO `eb_system_config` (`menu_name`, `key`, `value`, `status`, `info`, `create_time`, `update_time`) VALUES
('首单邀请奖励开关', 'first_order_invite_reward_enable', '1', 1, '首单邀请奖励功能开关（1-开启，0-关闭）', NOW(), NOW());

-- 首单邀请奖励金额
INSERT INTO `eb_system_config` (`menu_name`, `key`, `value`, `status`, `info`, `create_time`, `update_time`) VALUES
('首单邀请奖励金额', 'first_order_invite_reward_amount', '5.00', 1, '首单邀请奖励固定金额', NOW(), NOW());

-- 首单邀请奖励模式
INSERT INTO `eb_system_config` (`menu_name`, `key`, `value`, `status`, `info`, `create_time`, `update_time`) VALUES
('首单邀请奖励模式', 'first_order_invite_reward_mode', '1', 1, '邀请奖励模式（0-绑定即奖励，1-首单完成后奖励）', NOW(), NOW());
```

## 管理后台配置

在 `SystemMemberLevelController.java` 中添加首单奖励配置管理接口：

```java
@ApiOperation(value = "获取首单邀请奖励配置")
@RequestMapping(value = "/first-order-reward-config", method = RequestMethod.GET)
public CommonResult<Map<String, Object>> getFirstOrderRewardConfig();

@ApiOperation(value = "更新首单邀请奖励配置")
@RequestMapping(value = "/first-order-reward-config", method = RequestMethod.POST)
public CommonResult<String> updateFirstOrderRewardConfig(@RequestBody Map<String, Object> configData);
```

## 测试验证

### 测试场景
1. **正常流程测试**：A邀请B → B绑定邀请码 → B完成TikTok订单 → A获得5元奖励
2. **重复订单测试**：B完成第二笔TikTok订单 → A不应再获得奖励
3. **非TikTok订单测试**：B完成普通订单 → A不应获得首单奖励
4. **功能开关测试**：关闭首单奖励功能 → 不应产生奖励
5. **模式切换测试**：切换回绑定即奖励模式 → 应恢复原有机制

### 验证要点
1. 奖励任务正确创建
2. 奖励金额准确（固定5元）
3. 账单记录完整
4. 用户余额更新正确
5. 不重复奖励

## 优势分析

### 方案一优势
1. **复用现有架构**：利用现有的任务调度和处理机制
2. **配置灵活**：支持两种模式切换
3. **向后兼容**：不影响现有功能
4. **易于维护**：代码结构清晰

### 实施风险
1. **数据一致性**：需要确保首单判断的准确性
2. **性能影响**：每次订单结算都需要检查首单状态
3. **业务逻辑复杂度**：增加了订单处理的复杂性

## 实施建议

1. **分阶段实施**：先实现核心功能，再添加管理界面
2. **充分测试**：重点测试边界情况和异常场景
3. **监控告警**：添加关键指标监控
4. **灰度发布**：先在小范围用户中测试
5. **回滚预案**：准备快速回滚到原有机制的方案
