# GENCO推广邀请机制深度分析

## 概述

GENCO电商系统实现了一套完整的推广邀请机制，包括推广海报生成、邀请码管理、推广关系绑定、多级佣金分发等功能。本文档详细分析该机制的技术实现和业务流程。

## 1. 推广海报生成机制

### 1.1 海报数据结构

```java
@ApiModel(value="UserSpreadBannerResponse对象", description="用户推广海报")
public class UserSpreadBannerResponse implements Serializable {
    @ApiModelProperty(value = "id")
    private Integer id;
    
    @ApiModelProperty(value = "名称")
    private String title;
    
    @ApiModelProperty(value = "背景图")
    private String pic;
}
```

### 1.2 海报获取接口

**接口路径**: `GET /api/front/user/spread/banner`

```java
/**
 * 海报背景图
 */
@ApiOperation(value = "推广海报图")
@RequestMapping(value = "/user/spread/banner", method = RequestMethod.GET)
public CommonResult<List<UserSpreadBannerResponse>> getSpreadBannerList() {
    return CommonResult.success(userCenterService.getSpreadBannerList());
}
```

### 1.3 海报数据来源

```java
/**
 * 推广海报图
 * @return List<SystemGroupData>
 */
@Override
public List<UserSpreadBannerResponse> getSpreadBannerList() {
    return systemGroupDataService.getListByGid(Constants.GROUP_DATA_ID_SPREAD_BANNER_LIST, UserSpreadBannerResponse.class);
}
```

### 1.4 海报生成流程

1. **后台配置**: 系统管理员在后台配置推广海报模板
2. **数据存储**: 海报数据存储在`SystemGroupData`表中，通过`GROUP_DATA_ID_SPREAD_BANNER_LIST`分组
3. **模板获取**: 用户调用接口获取可用的海报模板列表
4. **个性化生成**: 前端根据模板和用户信息（邀请码、推广链接）生成个性化海报

## 2. 邀请码生成与管理

### 2.1 邀请码生成算法

```java
/**
 * 生成5个大写字母+1个数字的邀请码，随机组合成6位字符串
 * @return 邀请码
 */
public static String generateInviteCode() {
    String letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    String digits = "0123456789";
    char[] code = new char[6];
    
    // 先生成5个字母
    for (int i = 0; i < 5; i++) {
        code[i] = letters.charAt((int) (Math.random() * letters.length()));
    }
    
    // 最后1位为数字
    code[5] = digits.charAt((int) (Math.random() * digits.length()));
    
    // 打乱顺序
    for (int i = code.length - 1; i > 0; i--) {
        int j = (int) (Math.random() * (i + 1));
        char temp = code[i];
        code[i] = code[j];
        code[j] = temp;
    }
    
    return new String(code);
}
```

### 2.2 用户注册时邀请码分配

```java
// 生成唯一的邀请码
String inviteCode;
do {
    inviteCode = CommonUtil.generateInviteCode();
} while (userDao.selectCount(new QueryWrapper<User>().eq("invite_code", inviteCode)) > 0);
user.setInviteCode(inviteCode);
```

### 2.3 邀请码特点

- **组成规则**: 6位字符组成：5个大写字母 + 1个数字
- **随机性**: 随机打乱顺序，增加随机性
- **唯一性**: 系统确保唯一性，避免重复
- **自动分配**: 每个用户注册时自动分配

## 3. 邀请关系绑定机制

### 3.1 邀请码绑定接口

**接口路径**: `POST /api/front/user/bindInviteCode`

```java
/**
 * 绑定邀请码（登录状态）
 * @param inviteCode 邀请码
 * @return 绑定结果
 */
@ApiOperation(value = "绑定邀请码（登录状态）")
@RequestMapping(value = "/user/bindInviteCode", method = RequestMethod.POST)
public CommonResult<UserSpreadPeopleItemResponse> bindInviteCode(@RequestParam String inviteCode) {
    return CommonResult.success(userService.bindInviteCode(inviteCode));
}
```

### 3.2 邀请码绑定逻辑

```java
@Override
public UserSpreadPeopleItemResponse bindInviteCode(String inviteCode) {
    // 只允许登录用户
    User currentUser = getInfo();
    if (currentUser == null) {
        throw new CrmebException("请先登录");
    }
    if (currentUser.getSpreadUid() != null && currentUser.getSpreadUid() > 0) {
        throw new CrmebException("已绑定过邀请关系");
    }
    
    // 查找邀请码所属用户
    User inviter = userDao.selectOne(new QueryWrapper<User>().eq("invite_code", inviteCode));
    if (inviter == null) {
        throw new CrmebException("邀请码不存在");
    }
    if (inviter.getUid().equals(currentUser.getUid())) {
        throw new CrmebException("不能绑定自己的邀请码");
    }
    
    // 建立推广关系
    Boolean res = bindSpread(currentUser, inviter.getUid());
    if (!res) {
        throw new CrmebException("绑定关系更新失败");
    }
    
    // 返回邀请人信息...
}
```

### 3.3 推广关系绑定验证

```java
/**
 * 检测能否绑定关系
 * @param user      当前用户
 * @param spreadUid 推广员Uid
 * @param type      用户类型:new-新用户，old—老用户
 * @return Boolean
 */
public Boolean checkBingSpread(User user, Integer spreadUid, String type) {
    // 基础验证
    if (ObjectUtil.isNull(spreadUid)) return false;
    if (spreadUid <= 0 || user.getSpreadUid() > 0) return false;
    if (ObjectUtil.isNotNull(user.getUid()) && user.getUid().equals(spreadUid)) return false;
    
    // 判断分销功能是否启用
    String isOpen = systemConfigService.getValueByKey(Constants.CONFIG_KEY_STORE_BROKERAGE_IS_OPEN);
    if (StrUtil.isBlank(isOpen) || isOpen.equals("0")) return false;
    
    if (type.equals("old")) {
        // 判断分销关系绑定类型（所有、新用户）
        String bindType = systemConfigService.getValueByKey(Constants.CONFIG_KEY_DISTRIBUTION_TYPE);
        if (StrUtil.isBlank(bindType) || bindType.equals("1")) return false;
    }
    
    // 查询推广员
    User spreadUser = getById(spreadUid);
    if (ObjectUtil.isNull(spreadUser) || !spreadUser.getStatus()) return false;
    
    // 指定分销不是推广员不绑定
    if (!spreadUser.getIsPromoter()) return false;
    
    // 下级不能绑定自己的上级为自己的下级
    if (ObjectUtil.isNotNull(user.getUid()) && spreadUser.getSpreadUid().equals(user.getUid())) return false;
    
    return true;
}
```

### 3.4 验证规则

1. **基础验证**: 推广人ID有效、用户未绑定过推广关系、不能绑定自己
2. **功能开关**: 系统分销功能必须开启
3. **绑定类型**: 区分新用户和老用户的绑定权限
4. **推广员资格**: 推广人必须是有效的推广员
5. **循环绑定防护**: 防止A->B->A的循环绑定

## 4. 推广奖励机制

### 4.1 佣金分配触发

```java
/**
 * 计算佣金，生成佣金记录
 */
List<UserBrokerageRecord> recordList = assignCommission(storeOrder);

// 分销员逻辑
if (!user.getIsPromoter()) {
    String funcStatus = systemConfigService.getValueByKey(SysConfigConstants.CONFIG_KEY_BROKERAGE_FUNC_STATUS);
    if ("1".equals(funcStatus)) {
        String broQuota = systemConfigService.getValueByKey(SysConfigConstants.CONFIG_KEY_STORE_BROKERAGE_QUOTA);
        if (!"-1".equals(broQuota) && storeOrder.getPayPrice().compareTo(new BigDecimal(broQuota)) >= 0) {
            user.setIsPromoter(true);
            user.setPromoterTime(cn.hutool.core.date.DateUtil.date());
        }
    }
}
```

### 4.2 佣金计算规则

```java
/**
 * 分配佣金
 * @param storeOrder 订单
 * @return List<UserBrokerageRecord>
 */
private List<UserBrokerageRecord> assignCommission(StoreOrder storeOrder) {
    // 检测商城是否开启分销功能
    String isOpen = systemConfigService.getValueByKey(Constants.CONFIG_KEY_STORE_BROKERAGE_IS_OPEN);
    if (StrUtil.isBlank(isOpen) || "0".equals(isOpen)) {
        return CollUtil.newArrayList();
    }
    
    // 营销产品不参与
    if (storeOrder.getCombinationId() > 0 || storeOrder.getSeckillId() > 0 || storeOrder.getBargainId() > 0) {
        return CollUtil.newArrayList();
    }
    
    // 查找订单所属人信息
    User user = userService.getById(storeOrder.getUid());
    
    // 当前用户不存在 没有上级 或者 当用用户上级时自己  直接返回
    if (null == user.getSpreadUid() || user.getSpreadUid() < 1 || user.getSpreadUid().equals(storeOrder.getUid())) {
        return CollUtil.newArrayList();
    }
    
    // 继续佣金计算逻辑...
}
```

### 4.3 推广人数统计更新

```java
/**
 * 更新推广员推广数
 * @param uid  uid
 * @param type add or sub
 */
public Boolean updateSpreadCountByUid(Integer uid, String type) {
    UpdateWrapper<User> updateWrapper = new UpdateWrapper<>();
    if (type.equals("add")) {
        updateWrapper.setSql("spread_count = spread_count + 1");
    } else {
        updateWrapper.setSql("spread_count = spread_count - 1");
    }
    updateWrapper.eq("uid", uid);
    return update(updateWrapper);
}
```

## 5. 完整推广流程

### 5.1 业务流程图

```mermaid
graph TD
    A[用户A生成推广海报] --> B[海报包含邀请码/推广链接]
    B --> C[用户B看到海报]
    C --> D[用户B注册/登录系统]
    D --> E[用户B输入邀请码]
    E --> F[系统验证邀请码]
    F --> G{验证通过?}
    G -->|是| H[建立推广关系]
    G -->|否| I[绑定失败]
    H --> J[更新推广人数统计]
    J --> K[用户B下单购买]
    K --> L[触发佣金计算]
    L --> M[生成佣金记录]
    M --> N[推广人A获得奖励]
```

### 5.2 详细流程说明

1. **海报生成阶段**
   - 用户A登录系统，访问推广中心
   - 系统提供多种海报模板供选择
   - 用户A选择模板，系统生成包含其邀请码的个性化海报
   - 用户A分享海报到社交媒体或朋友圈

2. **邀请绑定阶段**
   - 用户B看到海报，通过邀请码或链接进入系统
   - 用户B完成注册或登录
   - 用户B主动输入邀请码或系统自动识别
   - 系统验证邀请码有效性和绑定资格
   - 验证通过后建立推广关系，更新相关统计

3. **奖励发放阶段**
   - 用户B在系统内产生消费行为
   - 系统检测到下级用户消费，触发佣金计算
   - 根据商品设置和系统配置计算佣金金额
   - 生成佣金记录，更新推广人A的佣金余额
   - 推广人A可查看佣金明细和申请提现

## 6. 奖励机制详解

### 6.1 多级分销体系

- **一级推广**: 直接邀请的用户，获得一级佣金
- **二级推广**: 间接邀请的用户，获得二级佣金
- **三级推广**: 部分场景支持三级分销

### 6.2 佣金计算方式

1. **固定佣金**: 商品设置固定分佣金额
2. **比例佣金**: 按订单金额的一定比例计算
3. **等级佣金**: 根据推广员等级设置不同比例

### 6.3 奖励发放机制

1. **即时奖励**: 注册绑定时的即时奖励
2. **订单佣金**: 下级用户消费时的佣金奖励
3. **冻结机制**: 佣金可能有冻结期，防止恶意刷单
4. **提现功能**: 累积佣金可申请提现

### 6.4 佣金记录数据结构

```java
@ApiModel(value = "UserBrokerageRecord对象", description = "用户佣金记录表")
public class UserBrokerageRecord implements Serializable {
    @ApiModelProperty(value = "记录id")
    private Integer id;

    @ApiModelProperty(value = "用户uid")
    private Integer uid;

    @ApiModelProperty(value = "关联id（orderNo,提现id）")
    private String linkId;

    @ApiModelProperty(value = "关联类型（order,extract，yue）")
    private String linkType;

    @ApiModelProperty(value = "类型：1-增加，2-扣减（提现）")
    private Integer type;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "佣金金额")
    private BigDecimal price;

    @ApiModelProperty(value = "剩余")
    private BigDecimal balance;

    @ApiModelProperty(value = "备注")
    private String mark;

    @ApiModelProperty(value = "状态：1-待生效，2-已生效，3-已失效")
    private Integer status;

    @ApiModelProperty(value = "冻结期")
    private Integer frozenTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
```

## 7. 系统配置项

### 7.1 主要配置参数

推广机制涉及的主要配置：

- `CONFIG_KEY_STORE_BROKERAGE_IS_OPEN`: 分销功能开关
- `CONFIG_KEY_DISTRIBUTION_TYPE`: 分销关系绑定类型
- `CONFIG_KEY_STORE_BROKERAGE_RATE_ONE`: 一级分销比例
- `CONFIG_KEY_STORE_BROKERAGE_RATE_TWO`: 二级分销比例
- `CONFIG_KEY_STORE_BROKERAGE_QUOTA`: 成为分销员的消费门槛
- `CONFIG_KEY_BROKERAGE_FUNC_STATUS`: 分销功能状态

### 7.2 配置说明

1. **分销功能开关**: 控制整个分销系统的启用状态
2. **绑定类型**: 控制新用户和老用户的绑定权限
3. **分销比例**: 设置不同级别的佣金分配比例
4. **消费门槛**: 设置成为分销员的最低消费要求

## 8. 核心接口汇总

### 8.1 推广相关接口

| 接口路径 | 方法 | 功能描述 |
|---------|------|----------|
| `/user/spread/banner` | GET | 获取推广海报模板列表 |
| `/user/bindSpread` | POST | 绑定推广关系（通过推广人ID） |
| `/user/bindInviteCode` | POST | 绑定邀请码（登录状态） |
| `/user/spread/people` | GET | 获取推广用户列表 |
| `/user/spread/order` | GET | 获取推广订单列表 |
| `/user/brokerage/record` | GET | 获取佣金记录列表 |

### 8.2 数据表结构

主要涉及的数据表：

- `eb_user`: 用户表，存储推广关系和邀请码
- `eb_user_brokerage_record`: 佣金记录表
- `eb_system_group_data`: 系统配置数据表（海报模板）
- `eb_store_order`: 订单表，触发佣金计算
- `eb_user_bill`: 用户账单表，记录资金变动

## 9. 技术特点

### 9.1 设计优势

1. **完整性**: 覆盖从海报生成到奖励发放的完整流程
2. **安全性**: 多重验证机制防止恶意绑定和刷单
3. **灵活性**: 支持多种佣金计算方式和配置选项
4. **可扩展性**: 支持多级分销和不同等级的推广员

### 9.2 技术实现

1. **事务管理**: 使用TransactionTemplate确保数据一致性
2. **缓存机制**: 合理使用Redis缓存提升性能
3. **异步处理**: 佣金计算和统计更新采用异步处理
4. **配置化**: 核心参数支持动态配置，便于运营调整

## 10. 总结

GENCO推广邀请机制是一套设计完善的营销系统，通过海报生成、邀请码管理、关系绑定验证、多级奖励分发等功能模块，构建了完整的用户推广体系。该机制不仅技术实现稳健，业务逻辑清晰，还具备良好的扩展性和配置灵活性，能够有效支撑电商平台的推广营销需求。

系统通过严格的验证机制防止恶意行为，通过灵活的配置支持不同的运营策略，通过完善的记录机制确保奖励分发的准确性和可追溯性，是一套值得参考的推广邀请系统实现方案。
